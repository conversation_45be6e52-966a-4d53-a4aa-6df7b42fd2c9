﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject ICosigningCommentHelper CommentHelper
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<div class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-main-paper" Elevation="3">
            <!-- Professional Header with Gradient -->
            <div class="cosigning-header-section">
                <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-4">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                        <div class="header-icon-wrapper">
                            <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                     Color="Color.Inherit"
                                     Size="Size.Large" />
                        </div>
                        <div>
                            <MudText Typo="Typo.h5" Class="header-title">
                                Document Signature
                            </MudText>
                            <MudText Typo="Typo.body2" Class="header-subtitle">
                                @PatientName - @DateTime.Now.ToString("MMM dd, yyyy")
                            </MudText>
                        </div>
                    </MudStack>
                    <MudChip T="string"
                             Size="Size.Medium"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip-modern">
                        @GetStatusText()
                    </MudChip>
                </MudStack>
            </div>

            <!-- Main Content Area -->
            <div class="cosigning-content-area">

                <!-- Current Signature Status Display -->
                @if (CurrentCosigning.IsSigned || CurrentCosigning.IsCosigned)
                {
                    <div class="signature-status-section">
                        <MudPaper Class="signature-status-card" Elevation="1">
                            <MudStack Spacing="3">
                                @if (CurrentCosigning.IsSigned)
                                {
                                    <div class="signature-item">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Color="Color.Success" Size="Size.Medium" />
                                            <div>
                                                <MudText Typo="Typo.body1" Class="signature-text">
                                                    <strong>Electronically signed by:</strong> @CurrentCosigning.SignerName
                                                </MudText>
                                                <MudText Typo="Typo.caption" Class="signature-date">
                                                    @CurrentCosigning.Date?.ToString("MMM dd, yyyy 'at' h:mm tt")
                                                </MudText>
                                            </div>
                                        </MudStack>
                                    </div>
                                }

                                @if (CurrentCosigning.IsCosigned)
                                {
                                    <div class="signature-item">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudIcon Icon="@Icons.Material.Filled.VerifiedUser" Color="Color.Primary" Size="Size.Medium" />
                                            <div>
                                                <MudText Typo="Typo.body1" Class="signature-text">
                                                    <strong>Electronically co-signed by:</strong> @CurrentCosigning.CosignerName
                                                </MudText>
                                                <MudText Typo="Typo.caption" Class="signature-date">
                                                    @CurrentCosigning.LastUpdated?.ToString("MMM dd, yyyy 'at' h:mm tt")
                                                </MudText>
                                            </div>
                                        </MudStack>
                                    </div>
                                }
                            </MudStack>
                        </MudPaper>
                    </div>
                }

                <!-- Comments Section (if any exist) -->
                @if (ActiveReviewRequest != null && !string.IsNullOrEmpty(ActiveReviewRequest.CommentsJson) && ActiveReviewRequest.CommentsJson != "[]")
                {
                    <div class="comments-section">
                        <MudText Typo="Typo.h6" Class="section-heading">
                            <MudIcon Icon="@Icons.Material.Filled.Comment" Size="Size.Small" Class="mr-2" />
                            Review Comments
                        </MudText>
                        <SectionCommentComponent CommentsJson="@ActiveReviewRequest.CommentsJson"
                                                 IsReadOnly="true"
                                                 ShowResolvedComments="true" />
                    </div>
                }

                <!-- Signing/Cosigning Action Form -->
                @if (!CurrentCosigning.IsLocked && (!CurrentCosigning.IsSigned || (CurrentCosigning.IsSigned && ActiveReviewRequest?.Status == CosigningRequestStatus.ChangesRequested)))
                {
                    <div class="action-form-section">
                        <MudPaper Class="action-form-card" Elevation="2">
                            <div class="action-form-header">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Medium" />
                                    <MudText Typo="Typo.h6" Class="form-title">
                                        @(CurrentCosigning.IsSigned ? "Update Signature" : "Sign Document")
                                    </MudText>
                                </MudStack>
                            </div>

                            <div class="action-form-content">
                                <MudStack Spacing="4">

                                    <!-- Action Selection Radio Buttons -->
                                    <div class="action-selection">
                                        <MudText Typo="Typo.subtitle1" Class="selection-label">Choose an action:</MudText>
                                        <MudRadioGroup @bind-SelectedOption="_selectedAction" T="string" Class="action-radio-group">
                                            <MudStack Spacing="3">
                                                <MudRadio T="string" Option="@("sign")" Color="Color.Primary" Class="action-radio">
                                                    <div class="radio-option-content">
                                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                                            <div class="radio-icon-wrapper sign-icon">
                                                                <MudIcon Icon="@Icons.Material.Filled.Draw" Size="Size.Medium" />
                                                            </div>
                                                            <div>
                                                                <MudText Typo="Typo.body1" Class="radio-title">
                                                                    Sign and Lock Document
                                                                </MudText>
                                                                <MudText Typo="Typo.caption" Class="radio-description">
                                                                    Complete the signing process and lock the document
                                                                </MudText>
                                                            </div>
                                                        </MudStack>
                                                    </div>
                                                </MudRadio>

                                                <MudRadio T="string" Option="@("cosign")" Color="Color.Info" Class="action-radio">
                                                    <div class="radio-option-content">
                                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                                            <div class="radio-icon-wrapper cosign-icon">
                                                                <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Medium" />
                                                            </div>
                                                            <div>
                                                                <MudText Typo="Typo.body1" Class="radio-title">
                                                                    Sign and Request Co-signature
                                                                </MudText>
                                                                <MudText Typo="Typo.caption" Class="radio-description">
                                                                    Sign first, then send to another provider for review
                                                                </MudText>
                                                            </div>
                                                        </MudStack>
                                                    </div>
                                                </MudRadio>
                                            </MudStack>
                                        </MudRadioGroup>
                                    </div>

                                    <!-- Provider Selection (only show when cosign is selected) -->
                                    @if (_selectedAction == "cosign")
                                    {
                                        <div class="provider-selection">
                                            <MudAlert Severity="Severity.Info" Dense="true" Class="provider-alert">
                                                <MudIcon Icon="@Icons.Material.Filled.Info" Size="Size.Small" Class="mr-2" />
                                                Select a provider to review and co-sign this document
                                            </MudAlert>

                                            <MudSelect @bind-Value="_selectedReviewProvider"
                                                       Label="Select Reviewing Provider"
                                                       Variant="Variant.Outlined"
                                                       FullWidth="true"
                                                       Required="true"
                                                       Class="provider-select">
                                                @foreach (var provider in _providerList)
                                                {
                                                    <MudSelectItem Value="@provider">
                                                        <div class="provider-option">
                                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="3">
                                                                <MudAvatar Size="Size.Medium" Class="provider-avatar">
                                                                    @provider.UserName?.Substring(0, 1).ToUpper()
                                                                </MudAvatar>
                                                                <div>
                                                                    <MudText Typo="Typo.body1" Class="provider-name">
                                                                        @provider.UserName
                                                                    </MudText>
                                                                    <MudText Typo="Typo.caption" Class="provider-role">
                                                                        @(provider.FirstName + " " + provider.LastName)
                                                                    </MudText>
                                                                </div>
                                                            </MudStack>
                                                        </div>
                                                    </MudSelectItem>
                                                }
                                            </MudSelect>
                                        </div>
                                    }

                                    <!-- Optional Comment Section -->
                                    <div class="comment-section">
                                        <MudTextField @bind-Value="_signatureComment"
                                                      Label="Add a comment (optional)"
                                                      Variant="Variant.Outlined"
                                                      Lines="3"
                                                      FullWidth="true"
                                                      Placeholder="Add any notes or comments about this signature..."
                                                      Class="comment-field" />
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="action-buttons">
                                        <MudStack Row Justify="Justify.FlexEnd" Spacing="3">
                                            <MudButton Variant="Variant.Text"
                                                       Color="Color.Default"
                                                       StartIcon="@Icons.Material.Filled.Cancel"
                                                       OnClick="@CancelAction"
                                                       Class="cancel-button">
                                                Cancel
                                            </MudButton>

                                            <MudButton Variant="Variant.Filled"
                                                       Color="@(_selectedAction == "sign" ? Color.Success : Color.Info)"
                                                       StartIcon="@(_selectedAction == "sign" ? Icons.Material.Filled.Draw : Icons.Material.Filled.Send)"
                                                       OnClick="ProcessAction"
                                                       Disabled="@(IsProcessing || (_selectedAction == "cosign" && _selectedReviewProvider == null))"
                                                       Class="@(_selectedAction == "sign" ? "sign-button" : "cosign-button")">
                                                @if (IsProcessing)
                                                {
                                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                                    <span class="ml-2">Processing...</span>
                                                }
                                                else
                                                {
                                                    @(_selectedAction == "sign" ? "Sign Document" : "Send Co-sign Request")
                                                }
                                            </MudButton>
                                        </MudStack>
                                    </div>
                                </MudStack>
                            </div>
                        </MudPaper>
                    </div>
                }

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">
                <!-- Primary Signature Display -->
                <MudTextField Variant="Variant.Outlined"
                              Value="@GetSignatureText()"
                              ReadOnly="true"
                              FullWidth="true"
                              Lines="3"
                              Multiline="true"
                              Style="font-family: monospace; background: #f8fff8;" />

                <!-- GitHub-Style Review Status -->
                @if (ActiveReviewRequest != null)
                {
                    <MudPaper Class="review-status-section" Elevation="2" Style="border-radius: 8px; overflow: hidden;">
                        @if (ActiveReviewRequest.Status == CosigningRequestStatus.Pending)
                        {
                            <!-- Pending Review -->
                            <div style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["PendingReview"]
                                    </MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                        @ActiveReviewRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>
                            </div>
                            <div style="background: #fff3e0; padding: 16px;">
                                <MudText Typo="Typo.body2">
                                    @Localizer["WaitingForReviewFrom"]: <strong>@ActiveReviewRequest.ReviewerName</strong>
                                </MudText>
                            </div>
                        }
                        else if (ActiveReviewRequest.Status == CosigningRequestStatus.Approved)
                        {
                            <!-- Approved -->
                            <div style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); color: white; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["ReviewApproved"]
                                    </MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                        @ActiveReviewRequest.ReviewedDate?.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>
                            </div>
                            <div style="background: #e8f5e8; padding: 16px;">
                                <MudText Typo="Typo.body2">
                                    @Localizer["ApprovedBy"]: <strong>@ActiveReviewRequest.ReviewerName</strong>
                                </MudText>
                                <MudAlert Severity="Severity.Success" Dense="true" Style="margin-top: 8px;">
                                    @Localizer["DocumentReadyToLock"]
                                </MudAlert>
                            </div>
                        }
                        else if (ActiveReviewRequest.Status == CosigningRequestStatus.ChangesRequested)
                        {
                            <!-- Changes Requested -->
                            <div style="background: linear-gradient(135deg, #d73a49 0%, #cb2431 100%); color: white; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Comment" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["ChangesRequested"]
                                    </MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                        @ActiveReviewRequest.ReviewedDate?.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>
                            </div>
                            <div style="background: #ffeef0; padding: 16px;">
                                <MudText Typo="Typo.body2" Style="margin-bottom: 12px;">
                                    @Localizer["ReviewerRequestedChanges"]: <strong>@ActiveReviewRequest.ReviewerName</strong>
                                </MudText>

                                <!-- Comments Display -->
                                <MudStack Spacing="2">
                                    @foreach (var comment in CommentHelper.GetComments(ActiveReviewRequest.CommentsJson).OrderBy(c => c.CommentDate))
                                    {
                                        <div class="github-comment" style="border: 1px solid #d73a49; border-radius: 6px; background: white; overflow: hidden;">
                                            <!-- Comment Header -->
                                            <div style="background: #f6f8fa; border-bottom: 1px solid #d73a49; padding: 8px 12px;">
                                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                    <MudAvatar Size="Size.Small" Style="background: #d73a49; color: white; font-weight: 600;">
                                                        @comment.CommenterName.Substring(0, 1).ToUpper()
                                                    </MudAvatar>
                                                    <MudText Typo="Typo.caption" Style="font-weight: 600; color: #24292e;">
                                                        @comment.CommenterName
                                                    </MudText>
                                                    <MudText Typo="Typo.caption" Style="color: #586069; font-size: 0.75rem;">
                                                        @comment.CommentDate.ToString("MMM dd, HH:mm")
                                                    </MudText>
                                                    <MudSpacer />
                                                    @if (comment.IsResolved)
                                                    {
                                                        <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                            @Localizer["Resolved"]
                                                        </MudChip>
                                                    }
                                                    else
                                                    {
                                                        <MudButton Size="Size.Small"
                                                                   Color="Color.Success"
                                                                   Variant="Variant.Text"
                                                                   OnClick="@(() => ResolveComment(comment.Id))"
                                                                   StartIcon="@Icons.Material.Filled.Check">
                                                            @Localizer["Resolve"]
                                                        </MudButton>
                                                    }
                                                </MudStack>
                                            </div>

                                            <!-- Comment Body -->
                                            <div style="padding: 12px;">
                                                <MudText Typo="Typo.body2" Style="line-height: 1.5; color: #24292e;">
                                                    @comment.Comment
                                                </MudText>
                                            </div>
                                        </div>
                                    }
                                </MudStack>
                            </div>
                        }
                    </MudPaper>
                }

                <!-- Lock Button (only show when document is signed/cosigned) -->
                @if (CurrentCosigning.IsSigned && !CurrentCosigning.IsLocked &&
                     (ActiveReviewRequest == null || ActiveReviewRequest.Status == CosigningRequestStatus.Approved || CurrentCosigning.IsCosigned))
                {
                    <div class="lock-section">
                        <MudPaper Class="lock-card" Elevation="1">
                            <MudStack Row AlignItems="AlignItems.Center" Justify="Justify.SpaceBetween" Class="pa-3">
                                <div>
                                    <MudText Typo="Typo.body1" Class="lock-title">
                                        Ready to Lock Document
                                    </MudText>
                                    <MudText Typo="Typo.caption" Class="lock-subtitle">
                                        Finalize and secure this document
                                    </MudText>
                                </div>
                                <MudButton Variant="Variant.Filled"
                                           Color="Color.Warning"
                                           StartIcon="@Icons.Material.Filled.Lock"
                                           OnClick="LockDocument"
                                           Disabled="@IsProcessing"
                                           Class="lock-button">
                                    @if (IsProcessing)
                                    {
                                        <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                        <span class="ml-2">Locking...</span>
                                    }
                                    else
                                    {
                                        <span>Lock Document</span>
                                    }
                                </MudButton>
                            </MudStack>
                        </MudPaper>
                    </div>
                }
            </MudStack>

            <!-- Navigation Links -->
            <div class="navigation-section">
                <MudPaper Class="navigation-card" Elevation="1">
                    <div class="navigation-header">
                        <MudText Typo="Typo.h6" Class="navigation-title">
                            Co-signing Management
                        </MudText>
                    </div>
                    <div class="navigation-content">
                        <MudStack Row Justify="Justify.SpaceEvenly" Class="pa-3">
                            <MudButton Variant="Variant.Text"
                                       Color="Color.Primary"
                                       StartIcon="@Icons.Material.Filled.Send"
                                       OnClick="@(() => NavigateToMyRequests())"
                                       Class="nav-button">
                                <div class="nav-button-content">
                                    <MudText Typo="Typo.body2" Class="nav-button-title">My Requests</MudText>
                                    <MudText Typo="Typo.caption" Class="nav-button-subtitle">Sent for review</MudText>
                                </div>
                            </MudButton>

                            <MudDivider Vertical="true" FlexItem="true" />

                            <MudButton Variant="Variant.Text"
                                       Color="Color.Info"
                                       StartIcon="@Icons.Material.Filled.RateReview"
                                       OnClick="@(() => NavigateToReviewRequests())"
                                       Class="nav-button">
                                <div class="nav-button-content">
                                    <MudText Typo="Typo.body2" Class="nav-button-title">Review Requests</MudText>
                                    <MudText Typo="Typo.caption" Class="nav-button-subtitle">Pending review</MudText>
                                </div>
                            </MudButton>
                        </MudStack>
                    </div>
                </MudPaper>
            </div>

        </MudPaper>
    }
</div>



<style>
    .cosigning-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .cosigning-header {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
        padding: 24px;
        border-radius: 8px 8px 0 0;
        margin-bottom: 0;
    }

    .cosigning-content {
        background: white;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .status-section {
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        padding: 20px 24px;
    }

    .signature-section {
        padding: 24px;
        background: white;
    }

    /* Action Form Styles */
    .action-form-section {
        margin: 24px 0;
    }

    .action-form-card {
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e9ecef;
    }

    .action-form-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 16px 20px;
        border-bottom: 1px solid #e9ecef;
    }

    .form-title {
        color: #1976d2;
        font-weight: 600;
    }

    .action-form-content {
        padding: 24px;
    }

    .action-selection {
        margin-bottom: 20px;
    }

    .selection-label {
        color: #495057;
        font-weight: 500;
        margin-bottom: 12px;
    }

    .action-radio-group .mud-radio {
        margin-bottom: 16px;
    }

    .radio-option-content {
        padding: 12px 16px;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        transition: all 0.2s ease;
        background: #f8f9fa;
    }

    .radio-option-content:hover {
        border-color: #1976d2;
        background: #f0f7ff;
    }

    .radio-icon-wrapper {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .sign-icon {
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    }

    .cosign-icon {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    }

    .radio-title {
        font-weight: 600;
        color: #212529;
    }

    .radio-description {
        color: #6c757d;
        margin-top: 4px;
    }

    .provider-selection {
        margin-top: 20px;
    }

    .provider-alert {
        margin-bottom: 16px;
    }

    .provider-avatar {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
        font-weight: 600;
    }

    .provider-name {
        font-weight: 500;
        color: #212529;
    }

    .provider-role {
        color: #6c757d;
    }

    .comment-section {
        margin-top: 20px;
    }

    .comment-field {
        border-radius: 6px;
    }

    .action-buttons {
        margin-top: 24px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .cancel-button {
        color: #6c757d;
        border-color: #6c757d;
    }

    .cancel-button:hover {
        background: #f8f9fa;
        border-color: #495057;
        color: #495057;
    }

    .sign-button {
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
        border: none;
        font-weight: 600;
    }

    .cosign-button {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
        border: none;
        font-weight: 600;
    }

    /* Lock Section Styles */
    .lock-section {
        margin: 24px 0;
    }

    .lock-card {
        border-radius: 8px;
        border: 1px solid #ffc107;
        background: #fff8e1;
    }

    .lock-title {
        color: #e65100;
        font-weight: 600;
    }

    .lock-subtitle {
        color: #f57c00;
    }

    .lock-button {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
        border: none;
        font-weight: 600;
    }

    /* Navigation Section Styles */
    .navigation-section {
        margin: 24px 0;
    }

    .navigation-card {
        border-radius: 8px;
        border: 1px solid #e9ecef;
        overflow: hidden;
    }

    .navigation-header {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 16px 20px;
        border-bottom: 1px solid #e9ecef;
    }

    .navigation-title {
        color: #495057;
        font-weight: 600;
    }

    .navigation-content {
        background: white;
    }

    .nav-button {
        padding: 16px 20px;
        border-radius: 0;
        transition: all 0.2s ease;
    }

    .nav-button:hover {
        background: #f8f9fa;
    }

    .nav-button-content {
        text-align: center;
    }

    .nav-button-title {
        font-weight: 600;
        color: inherit;
    }

    .nav-button-subtitle {
        color: #6c757d;
        margin-top: 4px;
    }

    /* Status Display Styles */
    .status-display {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;
    }

    .status-item {
        flex: 1;
        min-width: 200px;
    }

    .status-card {
        border-radius: 6px;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;
    }

    .status-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .status-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .signed-icon {
        background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    }

    .cosigned-icon {
        background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    }

    .locked-icon {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    }

    .status-label {
        color: #6c757d;
        font-weight: 500;
    }

    .status-value {
        color: #212529;
        font-weight: 600;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .cosigning-container {
            padding: 12px;
        }

        .cosigning-header {
            padding: 16px;
        }

        .action-form-content {
            padding: 16px;
        }

        .status-display {
            flex-direction: column;
        }

        .nav-button {
            padding: 12px 16px;
        }
    }
</style>