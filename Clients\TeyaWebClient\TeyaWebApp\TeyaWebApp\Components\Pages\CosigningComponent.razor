﻿@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Logging
@inject ICosigningService CosigningService
@inject ICosigningCommentHelper CommentHelper
@inject IStringLocalizer<TeyaAIScribeResource> Localizer
@inject ILogger<CosigningComponent> Logger
@inject ISnackbar Snackbar

<MudContainer MaxWidth="MaxWidth.False" Class="cosigning-container">
    @if (ShowCosigningSection)
    {
        <MudPaper Class="cosigning-paper" Elevation="2">
            <!-- Header Section -->
            <MudGrid AlignItems="Center" Justify="Justify.SpaceBetween" Class="cosigning-header">
                <MudItem>
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.VerifiedUser"
                                 Color="Color.Primary"
                                 Size="Size.Medium" />
                        <MudText Typo="Typo.h6" Class="header-title">
                            @Localizer["DocumentSignature"]
                        </MudText>
                    </MudStack>
                </MudItem>
                <MudItem>
                    <MudChip T="string"
                             Size="Size.Small"
                             Color="@GetStatusColor()"
                             Variant="Variant.Filled"
                             Class="status-chip">
                        @GetStatusText()
                    </MudChip>
                </MudItem>
            </MudGrid>

            <!-- GitHub-Style Signing/Cosigning Form -->
            @if (!CurrentCosigning.IsLocked)
            {
                <MudPaper Class="pa-4 mb-4" Style="background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px;">
                    <MudStack Spacing="4">
                        <!-- Header -->
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.RateReview" Color="Color.Primary" Size="Size.Medium" />
                            <MudText Typo="Typo.h6" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["SignOrRequestCosign"]
                            </MudText>
                        </MudStack>

                        <!-- Action Selection -->
                        <MudRadioGroup @bind-SelectedOption="_selectedAction" T="string">
                            <MudStack Spacing="2">
                                <MudRadio T="string" Option="@("sign")" Color="Color.Primary">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Draw" Size="Size.Small" />
                                        <MudText Typo="Typo.body1" Style="font-weight: 500;">
                                            @Localizer["SignDocument"]
                                        </MudText>
                                    </MudStack>
                                </MudRadio>

                                <MudRadio T="string" Option="@("cosign")" Color="Color.Info">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Small" />
                                        <MudText Typo="Typo.body1" Style="font-weight: 500;">
                                            @Localizer["RequestCosignature"]
                                        </MudText>
                                    </MudStack>
                                </MudRadio>
                            </MudStack>
                        </MudRadioGroup>

                        <!-- Provider Selection (only show when cosign is selected) -->
                        @if (_selectedAction == "cosign")
                        {
                            <MudAlert Severity="Severity.Info" Dense="true">
                                @Localizer["SelectProviderForCosignature"]
                            </MudAlert>

                            <MudSelect @bind-Value="_selectedReviewProvider"
                                       Label="@Localizer["SelectProvider"]"
                                       Variant="Variant.Outlined"
                                       FullWidth="true"
                                       Required="true">
                                @foreach (var provider in _providerList)
                                {
                                    <MudSelectItem Value="@provider">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudAvatar Size="Size.Small" Style="background: #1976d2; color: white; font-weight: 600;">
                                                @provider.UserName?.Substring(0, 1).ToUpper()
                                            </MudAvatar>
                                            <MudText>@provider.UserName</MudText>
                                        </MudStack>
                                    </MudSelectItem>
                                }
                            </MudSelect>
                        }

                        <!-- Comment Section -->
                        <MudTextField @bind-Value="_signatureComment"
                                      Label="@($"{Localizer["Comment"]} ({Localizer["Optional"]})")"
                                      Placeholder="@(_selectedAction == "cosign" ? Localizer["AddCommentForReviewer"] : Localizer["EnterSignatureComment"])"
                                      Variant="Variant.Outlined"
                                      FullWidth="true"
                                      Lines="3"
                                      Multiline="true"
                                      Style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;" />

                        <!-- Action Buttons -->
                        <MudStack Row Justify="Justify.FlexEnd" Spacing="2">
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="CancelAction"
                                       Size="Size.Medium"
                                       StartIcon="@Icons.Material.Filled.Close">
                                @Localizer["Cancel"]
                            </MudButton>

                            <MudButton Variant="Variant.Filled"
                                       Color="@(_selectedAction == "cosign" ? Color.Info : Color.Primary)"
                                       OnClick="ProcessAction"
                                       Disabled="@(IsProcessing || (_selectedAction == "cosign" && _selectedReviewProvider == null))"
                                       Size="Size.Medium"
                                       StartIcon="@(_selectedAction == "cosign" ? Icons.Material.Filled.Send : Icons.Material.Filled.Draw)">
                                @if (IsProcessing)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-left: 8px;">@Localizer["Processing"]</span>
                                }
                                else
                                {
                                    @(_selectedAction == "cosign" ? Localizer["SendForReview"] : Localizer["Sign"])
                                }
                            </MudButton>
                        </MudStack>
                    </MudStack>
                </MudPaper>
            }

            <!-- Signature Section -->
            <MudStack Class="signature-section" Spacing="3">
                <!-- Primary Signature Display -->
                <MudTextField Variant="Variant.Outlined"
                              Value="@GetSignatureText()"
                              ReadOnly="true"
                              FullWidth="true"
                              Lines="3"
                              Multiline="true"
                              Style="font-family: monospace; background: #f8fff8;" />

                <!-- GitHub-Style Review Status -->
                @if (ActiveReviewRequest != null)
                {
                    <MudPaper Class="review-status-section" Elevation="2" Style="border-radius: 8px; overflow: hidden;">
                        @if (ActiveReviewRequest.Status == CosigningRequestStatus.Pending)
                        {
                            <!-- Pending Review -->
                            <div style="background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); color: white; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Schedule" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["PendingReview"]
                                    </MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                        @ActiveReviewRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>
                            </div>
                            <div style="background: #fff3e0; padding: 16px;">
                                <MudText Typo="Typo.body2">
                                    @Localizer["WaitingForReviewFrom"]: <strong>@ActiveReviewRequest.ReviewerName</strong>
                                </MudText>
                            </div>
                        }
                        else if (ActiveReviewRequest.Status == CosigningRequestStatus.Approved)
                        {
                            <!-- Approved -->
                            <div style="background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%); color: white; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["ReviewApproved"]
                                    </MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                        @ActiveReviewRequest.ReviewedDate?.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>
                            </div>
                            <div style="background: #e8f5e8; padding: 16px;">
                                <MudText Typo="Typo.body2">
                                    @Localizer["ApprovedBy"]: <strong>@ActiveReviewRequest.ReviewerName</strong>
                                </MudText>
                                <MudAlert Severity="Severity.Success" Dense="true" Style="margin-top: 8px;">
                                    @Localizer["DocumentReadyToLock"]
                                </MudAlert>
                            </div>
                        }
                        else if (ActiveReviewRequest.Status == CosigningRequestStatus.ChangesRequested)
                        {
                            <!-- Changes Requested -->
                            <div style="background: linear-gradient(135deg, #d73a49 0%, #cb2431 100%); color: white; padding: 12px 16px;">
                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                    <MudIcon Icon="@Icons.Material.Filled.Comment" Size="Size.Small" />
                                    <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                        @Localizer["ChangesRequested"]
                                    </MudText>
                                    <MudSpacer />
                                    <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                        @ActiveReviewRequest.ReviewedDate?.ToString("MMM dd, yyyy 'at' h:mm tt")
                                    </MudText>
                                </MudStack>
                            </div>
                            <div style="background: #ffeef0; padding: 16px;">
                                <MudText Typo="Typo.body2" Style="margin-bottom: 12px;">
                                    @Localizer["ReviewerRequestedChanges"]: <strong>@ActiveReviewRequest.ReviewerName</strong>
                                </MudText>

                                <!-- Comments Display -->
                                <MudStack Spacing="2">
                                    @foreach (var comment in CommentHelper.GetComments(ActiveReviewRequest.CommentsJson).OrderBy(c => c.CommentDate))
                                    {
                                        <div class="github-comment" style="border: 1px solid #d73a49; border-radius: 6px; background: white; overflow: hidden;">
                                            <!-- Comment Header -->
                                            <div style="background: #f6f8fa; border-bottom: 1px solid #d73a49; padding: 8px 12px;">
                                                <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                    <MudAvatar Size="Size.Small" Style="background: #d73a49; color: white; font-weight: 600;">
                                                        @comment.CommenterName.Substring(0, 1).ToUpper()
                                                    </MudAvatar>
                                                    <MudText Typo="Typo.caption" Style="font-weight: 600; color: #24292e;">
                                                        @comment.CommenterName
                                                    </MudText>
                                                    <MudText Typo="Typo.caption" Style="color: #586069; font-size: 0.75rem;">
                                                        @comment.CommentDate.ToString("MMM dd, HH:mm")
                                                    </MudText>
                                                    <MudSpacer />
                                                    @if (comment.IsResolved)
                                                    {
                                                        <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                            @Localizer["Resolved"]
                                                        </MudChip>
                                                    }
                                                    else
                                                    {
                                                        <MudButton Size="Size.Small"
                                                                   Color="Color.Success"
                                                                   Variant="Variant.Text"
                                                                   OnClick="@(() => ResolveComment(comment.Id))"
                                                                   StartIcon="@Icons.Material.Filled.Check">
                                                            @Localizer["Resolve"]
                                                        </MudButton>
                                                    }
                                                </MudStack>
                                            </div>

                                            <!-- Comment Body -->
                                            <div style="padding: 12px;">
                                                <MudText Typo="Typo.body2" Style="line-height: 1.5; color: #24292e;">
                                                    @comment.Comment
                                                </MudText>
                                            </div>
                                        </div>
                                    }
                                </MudStack>
                            </div>
                        }
                    </MudPaper>
                }

                <!-- Lock Button (only show when document is signed/cosigned) -->
                @if (CurrentCosigning.IsSigned && !CurrentCosigning.IsLocked &&
                     (ActiveReviewRequest == null || ActiveReviewRequest.Status == CosigningRequestStatus.Approved || CurrentCosigning.IsCosigned))
                {
                    <MudStack Row Justify="Justify.FlexEnd" Style="margin-top: 16px;">
                        <MudButton Variant="Variant.Filled"
                                   Color="Color.Warning"
                                   StartIcon="@Icons.Material.Filled.Lock"
                                   OnClick="LockDocument"
                                   Disabled="@IsProcessing"
                                   Size="Size.Medium"
                                   Style="font-weight: 600;">
                            @Localizer["LockDocument"]
                        </MudButton>
                    </MudStack>
                }
            </MudStack>

            <!-- Comments Section -->
            @if (ActiveReviewRequest != null)
            {
                <MudPaper Class="comments-section" Elevation="1" Style="margin-top: 16px; padding: 16px; background: #f8f9fa; border-left: 4px solid #1976d2;">
                    <MudStack Spacing="2">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" Size="Size.Small" />
                            <MudText Typo="Typo.subtitle2" Style="font-weight: 600; color: #1976d2;">
                                @Localizer["ReviewComments"]
                            </MudText>
                        </MudStack>

                        <div class="comments-container" style="max-height: 300px; overflow-y: auto;">
                            @foreach (var comment in CommentHelper.GetComments(ActiveReviewRequest.CommentsJson).OrderBy(c => c.CommentDate))
                            {
                                <MudCard Class="comment-card" Elevation="1" Style="margin-bottom: 8px; background: white;">
                                    <MudCardContent Class="pa-3">
                                        <MudStack Row Justify="Justify.SpaceBetween" AlignItems="AlignItems.Start" Class="mb-2">
                                            <MudText Typo="Typo.caption" Style="font-weight: 600; color: #1976d2;">
                                                @comment.CommenterName
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="text-muted">
                                                @comment.CommentDate.ToString("MM/dd/yyyy HH:mm")
                                            </MudText>
                                        </MudStack>

                                        <MudText Typo="Typo.body2" Style="line-height: 1.5; margin-bottom: 8px;">
                                            @comment.Comment
                                        </MudText>

                                        @if (comment.IsResolved)
                                        {
                                            <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                @Localizer["Resolved"]
                                                @if (comment.ResolvedDate.HasValue)
                                                {
                                                    <text> - @comment.ResolvedDate.Value.ToString("MM/dd/yyyy")</text>
                                                }
                                            </MudChip>
                                        }
                                        else
                                        {
                                            <MudChip T="string" Color="Color.Warning" Size="Size.Small" Icon="@Icons.Material.Filled.Schedule">
                                                @Localizer["NeedsAttention"]
                                            </MudChip>
                                        }
                                    </MudCardContent>
                                </MudCard>
                            }
                        </div>
                    </MudStack>
                </MudPaper>
            }

            <!-- Review Request Status -->
            @if (ActiveReviewRequest != null)
            {
                <MudAlert Severity="@GetReviewRequestSeverity()"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="review-request-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@GetReviewRequestIcon()" Size="Size.Small" />
                        <div>
                            <MudText Typo="Typo.body2" Style="font-weight: 600;">
                                @GetReviewRequestStatusText()
                            </MudText>
                            <MudText Typo="Typo.caption">
                                @Localizer["RequestedFrom"]: @ActiveReviewRequest.ReviewerName
                            </MudText>
                            <MudText Typo="Typo.caption">
                                @Localizer["RequestedOn"]: @ActiveReviewRequest.RequestedDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </MudText>
                        </div>
                    </MudStack>
                </MudAlert>
            }

            <!-- GitHub-Style Review Comments -->
            @if (ActiveReviewRequest != null && ActiveReviewRequest.Status == CosigningRequestStatus.ChangesRequested)
            {
                <MudPaper Class="github-review-section" Elevation="2" Style="margin-top: 16px; border: 1px solid #d73a49; border-radius: 8px; overflow: hidden;">
                    <!-- Review Header -->
                    <div style="background: linear-gradient(135deg, #d73a49 0%, #cb2431 100%); color: white; padding: 12px 16px;">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudIcon Icon="@Icons.Material.Filled.RateReview" Size="Size.Small" />
                            <MudText Typo="Typo.subtitle1" Style="font-weight: 600;">
                                @ActiveReviewRequest.ReviewerName @Localizer["RequestedChanges"]
                            </MudText>
                            <MudSpacer />
                            <MudText Typo="Typo.caption" Style="opacity: 0.9;">
                                @ActiveReviewRequest.ReviewedDate?.ToString("MMM dd, yyyy 'at' h:mm tt")
                            </MudText>
                        </MudStack>
                    </div>

                    <!-- Comments Body -->
                    <div style="background: #ffeef0; padding: 16px;">
                        <MudAlert Severity="Severity.Warning" Dense="true" Class="mb-3" Style="border-left: 4px solid #d73a49;">
                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                <MudIcon Icon="@Icons.Material.Filled.Warning" Size="Size.Small" />
                                <MudText Style="font-weight: 500;">@Localizer["PleaseResolveComments"]</MudText>
                            </MudStack>
                        </MudAlert>

                        <!-- Comments List -->
                        <MudStack Spacing="3">
                            @foreach (var comment in CommentHelper.GetComments(ActiveReviewRequest.CommentsJson).OrderBy(c => c.CommentDate))
                            {
                                <div class="github-comment" style="border: 1px solid #d73a49; border-radius: 6px; background: white; overflow: hidden;">
                                    <!-- Comment Header -->
                                    <div style="background: #f6f8fa; border-bottom: 1px solid #d73a49; padding: 8px 12px;">
                                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                            <MudAvatar Size="Size.Small" Style="background: #d73a49; color: white;">
                                                @comment.CommenterName.Substring(0, 1).ToUpper()
                                            </MudAvatar>
                                            <MudText Typo="Typo.body2" Style="font-weight: 600;">
                                                @comment.CommenterName
                                            </MudText>
                                            <MudText Typo="Typo.caption" Class="text-muted">
                                                commented @comment.CommentDate.ToString("MMM dd, yyyy 'at' h:mm tt")
                                            </MudText>
                                            <MudSpacer />
                                            @if (comment.IsResolved)
                                            {
                                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                                    @Localizer["Resolved"]
                                                </MudChip>
                                            }
                                        </MudStack>
                                    </div>

                                    <!-- Comment Body -->
                                    <div style="padding: 12px;">
                                        <MudText Typo="Typo.body2" Style="line-height: 1.6; margin-bottom: 12px;">
                                            @comment.Comment
                                        </MudText>

                                        @if (!comment.IsResolved)
                                        {
                                            <MudStack Row Justify="Justify.FlexEnd">
                                                <MudButton Variant="Variant.Filled"
                                                           Color="Color.Success"
                                                           Size="Size.Small"
                                                           StartIcon="@Icons.Material.Filled.Check"
                                                           OnClick="@(() => ResolveComment(comment.Id))"
                                                           Disabled="@IsProcessing"
                                                           Style="border-radius: 6px;">
                                                    @Localizer["Resolve"]
                                                </MudButton>
                                            </MudStack>
                                        }
                                        else if (comment.ResolvedDate.HasValue)
                                        {
                                            <MudAlert Severity="Severity.Success" Dense="true" Style="margin-top: 8px;">
                                                <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                                                    <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" />
                                                    <MudText Typo="Typo.caption">
                                                        @Localizer["ResolvedOn"] @comment.ResolvedDate.Value.ToString("MMM dd, yyyy 'at' h:mm tt")
                                                    </MudText>
                                                </MudStack>
                                            </MudAlert>
                                        }
                                    </div>
                                </div>
                            }
                        </MudStack>

                        <!-- Re-request Review Instructions -->
                        @if (!HasUnresolvedComments())
                        {
                            <MudDivider Class="my-4" />
                            <MudStack Row Justify="Justify.Center">
                                <MudAlert Severity="Severity.Success" Dense="true" Style="margin-bottom: 12px;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" Size="Size.Small" />
                                        <MudText Style="font-weight: 500;">@Localizer["AllCommentsResolved"]</MudText>
                                    </MudStack>
                                </MudAlert>
                                <MudText Typo="Typo.body2" Style="text-align: center; color: #666;">
                                    @Localizer["UseFormAboveToRequestReviewAgain"]
                                </MudText>
                            </MudStack>
                        }
                    </div>
                </MudPaper>
            }

            <!-- Lock Status -->
            @if (CurrentCosigning.IsLocked)
            {
                <MudAlert Severity="Severity.Info"
                          Variant="Variant.Filled"
                          Dense="true"
                          Class="lock-alert">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="1">
                        <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Small" />
                        <MudText>@Localizer["DocumentLocked"]</MudText>
                    </MudStack>
                </MudAlert>
            }
        </MudPaper>
    }
</MudContainer>



<style>
    .cosigning-container {
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .cosigning-paper {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        margin-bottom: 16px;
        width: 100%;
    }

    .cosigning-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f0f0f0;
    }

    .header-title {
        font-weight: 600;
        color: #1976d2;
        margin: 0;
        font-size: 1rem;
    }

    .status-chip {
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 0.75rem;
    }

    .signature-section {
        margin-top: 0;
    }

    .signature-block {
        padding: 16px;
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        background: #ffffff;
        transition: all 0.2s ease;
    }

    .signature-block:hover {
        border-color: #1976d2;
        box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);
    }

    .signature-block.signed {
        background: #f8fff8;
        border-color: #4caf50;
    }

    .signature-title {
        font-weight: 600;
        color: #333;
        margin: 0;
        font-size: 0.875rem;
    }

    .signature-title.signed {
        color: #2e7d32;
    }

    .signature-button {
        min-width: 120px;
        font-weight: 500;
        text-transform: none;
        border-radius: 4px;
        font-size: 0.875rem;
    }

    .signature-divider {
        margin: 12px 0;
        opacity: 0.6;
    }

    .lock-alert {
        margin-top: 12px;
        border-radius: 4px;
    }

    .review-request-alert {
        margin-top: 12px;
        border-radius: 4px;
    }

    .comments-section {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        border-radius: 4px;
    }

    .comments-container {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .comment-card {
        border-radius: 4px;
        border: 1px solid #e0e0e0;
        transition: box-shadow 0.2s ease;
    }

    .comment-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .text-muted {
        color: #6c757d;
    }

    .github-review-section {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .github-comment {
        transition: box-shadow 0.2s ease;
    }

    .github-comment:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .github-comment .mud-avatar {
        font-weight: 600;
        font-size: 0.75rem;
    }
</style>