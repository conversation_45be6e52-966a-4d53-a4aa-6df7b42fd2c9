/* Section Comment Component Styles */
.section-comment-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 8px 0;
}

/* Comment Form Overlay */
.comment-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.comment-form-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.comment-form-header {
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
    color: white;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.comment-form-title {
    font-weight: 600;
    margin: 0;
}

.close-button {
    color: white !important;
}

.comment-form-body {
    padding: 20px;
}

/* Section Context */
.section-context {
    margin-bottom: 20px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1976d2;
}

.context-label {
    color: #666;
    font-weight: 500;
    margin-bottom: 4px;
}

.context-content {
    background: white;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.context-text {
    color: #333;
    line-height: 1.4;
    font-style: italic;
}

/* Comment Input */
.comment-input-section {
    margin-bottom: 20px;
}

.comment-input {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Form Actions */
.comment-form-actions {
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.cancel-button {
    border-radius: 6px;
    text-transform: none;
    font-weight: 500;
}

.submit-button {
    border-radius: 6px;
    text-transform: none;
    font-weight: 600;
    background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
}

/* Section Comments List */
.section-comments-list {
    margin: 12px 0;
}

.section-comment-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: white;
    margin-bottom: 8px;
    overflow: hidden;
    transition: all 0.2s ease;
}

.section-comment-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-comment-item.resolved {
    border-color: #4caf50;
    background: #f8fff8;
}

.section-comment-item.unresolved {
    border-color: #ff9800;
    background: #fffbf0;
}

/* Comment Item Header */
.comment-item-header {
    background: #f6f8fa;
    padding: 8px 12px;
    border-bottom: 1px solid #e0e0e0;
}

.comment-avatar {
    background: #1976d2 !important;
    color: white !important;
    font-weight: 600;
    font-size: 0.75rem;
}

.commenter-name {
    font-weight: 600;
    color: #24292e;
}

.comment-date {
    color: #586069;
    font-size: 0.75rem;
}

.resolve-button {
    border-radius: 4px;
    text-transform: none;
    font-size: 0.75rem;
    padding: 4px 8px;
    min-width: auto;
}

/* Comment Item Body */
.comment-item-body {
    padding: 12px;
}

.comment-text {
    line-height: 1.5;
    color: #24292e;
    margin-bottom: 8px;
}

.resolved-info {
    color: #4caf50;
    font-style: italic;
    margin-top: 8px;
}

/* Add Comment Section */
.add-comment-section {
    margin: 8px 0;
    text-align: center;
}

.add-comment-button {
    border-radius: 6px;
    text-transform: none;
    font-weight: 500;
    border-color: #1976d2;
    color: #1976d2;
    transition: all 0.2s ease;
}

.add-comment-button:hover {
    background: #1976d2;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .comment-form-modal {
        width: 95%;
        margin: 10px;
    }
    
    .comment-form-body {
        padding: 16px;
    }
    
    .comment-form-header {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .comment-form-modal {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0;
    }
    
    .comment-form-actions {
        flex-direction: column;
    }
    
    .comment-form-actions .mud-stack {
        flex-direction: column;
        gap: 8px;
    }
    
    .cancel-button,
    .submit-button {
        width: 100%;
    }
}

/* Focus States for Accessibility */
.add-comment-button:focus,
.resolve-button:focus,
.submit-button:focus,
.cancel-button:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

/* Animation for new comments */
.section-comment-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
