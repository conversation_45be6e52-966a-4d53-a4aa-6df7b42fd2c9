@using TeyaUIModels.Model
@using TeyaUIViewModels.ViewModel
@using Microsoft.Extensions.Localization
@inject ICosigningCommentHelper CommentHelper
@inject IStringLocalizer<TeyaAIScribeResource> Localizer

<div class="section-comment-container">
    @if (ShowCommentForm)
    {
        <!-- Comment Form -->
        <div class="comment-form-overlay" @onclick="CancelComment">
            <div class="comment-form-modal" @onclick:stopPropagation="true">
                <div class="comment-form-header">
                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                        <MudIcon Icon="@Icons.Material.Filled.Comment" Color="Color.Primary" Size="Size.Small" />
                        <MudText Typo="Typo.subtitle1" Class="comment-form-title">
                            @Localizer["AddComment"] - @SectionName
                        </MudText>
                        <MudSpacer />
                        <MudIconButton Icon="@Icons.Material.Filled.Close"
                                       Size="Size.Small"
                                       OnClick="CancelComment"
                                       Class="close-button" />
                    </MudStack>
                </div>

                <div class="comment-form-body">
                    <!-- Section Context -->
                    <div class="section-context">
                        <MudText Typo="Typo.caption" Class="context-label">
                            @Localizer["CommentingOn"]:
                        </MudText>
                        <div class="context-content">
                            @if (!string.IsNullOrEmpty(SectionContent))
                            {
                                <MudText Typo="Typo.body2" Class="context-text">
                                    @(SectionContent.Length > 200 ? SectionContent.Substring(0, 200) + "..." : SectionContent)
                                </MudText>
                            }
                            else
                            {
                                <MudText Typo="Typo.body2" Class="context-text">
                                    @SectionName @Localizer["Section"]
                                </MudText>
                            }
                        </div>
                    </div>

                    <!-- Comment Input -->
                    <div class="comment-input-section">
                        <MudTextField @bind-Value="CommentText"
                                      Label="@Localizer["YourComment"]"
                                      Placeholder="@Localizer["EnterCommentPlaceholder"]"
                                      Variant="Variant.Outlined"
                                      FullWidth="true"
                                      Lines="4"
                                      Multiline="true"
                                      Required="true"
                                      Class="comment-input" />
                    </div>

                    <!-- Action Buttons -->
                    <div class="comment-form-actions">
                        <MudStack Row Justify="Justify.FlexEnd" Spacing="2">
                            <MudButton Variant="Variant.Outlined"
                                       Color="Color.Secondary"
                                       OnClick="CancelComment"
                                       Size="Size.Medium"
                                       Class="cancel-button">
                                @Localizer["Cancel"]
                            </MudButton>
                            <MudButton Variant="Variant.Filled"
                                       Color="Color.Primary"
                                       OnClick="SubmitComment"
                                       Disabled="@(string.IsNullOrWhiteSpace(CommentText) || IsSubmitting)"
                                       Size="Size.Medium"
                                       Class="submit-button">
                                @if (IsSubmitting)
                                {
                                    <MudProgressCircular Size="Size.Small" Indeterminate="true" />
                                    <span style="margin-left: 8px;">@Localizer["Submitting"]</span>
                                }
                                else
                                {
                                    @Localizer["AddComment"]
                                }
                            </MudButton>
                        </MudStack>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (SectionComments.Any())
    {
        <!-- Existing Comments Display -->
        <div class="section-comments-list">
            @foreach (var comment in SectionComments.OrderBy(c => c.CommentDate))
            {
                <div class="section-comment-item @(comment.IsResolved ? "resolved" : "unresolved")">
                    <!-- Comment Header -->
                    <div class="comment-item-header">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                            <MudAvatar Size="Size.Small" Class="comment-avatar">
                                @comment.CommenterName.Substring(0, 1).ToUpper()
                            </MudAvatar>
                            <MudText Typo="Typo.caption" Class="commenter-name">
                                @comment.CommenterName
                            </MudText>
                            <MudText Typo="Typo.caption" Class="comment-date">
                                @comment.CommentDate.ToString("MMM dd, HH:mm")
                            </MudText>
                            <MudSpacer />
                            @if (comment.IsResolved)
                            {
                                <MudChip T="string" Color="Color.Success" Size="Size.Small" Icon="@Icons.Material.Filled.CheckCircle">
                                    @Localizer["Resolved"]
                                </MudChip>
                            }
                            else if (CanResolveComments)
                            {
                                <MudButton Size="Size.Small"
                                           Color="Color.Success"
                                           Variant="Variant.Text"
                                           OnClick="@(() => OnResolveComment.InvokeAsync(comment.Id))"
                                           StartIcon="@Icons.Material.Filled.Check"
                                           Class="resolve-button">
                                    @Localizer["Resolve"]
                                </MudButton>
                            }
                        </MudStack>
                    </div>

                    <!-- Comment Body -->
                    <div class="comment-item-body">
                        <MudText Typo="Typo.body2" Class="comment-text">
                            @comment.Comment
                        </MudText>
                        @if (comment.IsResolved && comment.ResolvedDate.HasValue)
                        {
                            <MudText Typo="Typo.caption" Class="resolved-info">
                                @Localizer["ResolvedOn"] @comment.ResolvedDate.Value.ToString("MMM dd, yyyy 'at' h:mm tt")
                                @if (!string.IsNullOrEmpty(comment.ResolvedByName))
                                {
                                    <text> @Localizer["By"] @comment.ResolvedByName</text>
                                }
                            </MudText>
                        }
                    </div>
                </div>
            }
        </div>
    }

    @if (!SectionComments.Any() && !ShowCommentForm)
    {
        <!-- Add Comment Button -->
        <div class="add-comment-section">
            <MudButton Variant="Variant.Outlined"
                       Color="Color.Primary"
                       OnClick="ShowCommentFormHandler"
                       Size="Size.Small"
                       StartIcon="@Icons.Material.Filled.Add"
                       Class="add-comment-button">
                @Localizer["AddComment"]
            </MudButton>
        </div>
    }
</div>

@code {
    [Parameter] public string SectionName { get; set; } = string.Empty;
    [Parameter] public string SectionContent { get; set; } = string.Empty;
    [Parameter] public List<CosigningComment> SectionComments { get; set; } = new();
    [Parameter] public bool CanResolveComments { get; set; } = false;
    [Parameter] public EventCallback<string> OnAddComment { get; set; }
    [Parameter] public EventCallback<Guid> OnResolveComment { get; set; }

    private bool ShowCommentForm = false;
    private string CommentText = string.Empty;
    private bool IsSubmitting = false;

    private void ShowCommentFormHandler()
    {
        ShowCommentForm = true;
        StateHasChanged();
    }

    private void CancelComment()
    {
        ShowCommentForm = false;
        CommentText = string.Empty;
        StateHasChanged();
    }

    private async Task SubmitComment()
    {
        if (string.IsNullOrWhiteSpace(CommentText)) return;

        try
        {
            IsSubmitting = true;
            await OnAddComment.InvokeAsync(CommentText);
            
            // Reset form
            CommentText = string.Empty;
            ShowCommentForm = false;
        }
        finally
        {
            IsSubmitting = false;
            StateHasChanged();
        }
    }
}
