/* Enhanced ReviewRequests Page Styling */

/* Head<PERSON> Gradient Animation */
.header-gradient {
    position: relative;
    overflow: hidden;
}

.header-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Status Summary Cards */
.status-summary-card {
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.status-summary-card:hover {
    transform: translateY(-2px);
    background: rgba(255,255,255,0.25) !important;
}

/* Refresh Button Animation */
.refresh-button {
    transition: all 0.3s ease;
}

.refresh-button:hover {
    transform: rotate(180deg);
    background: rgba(255,255,255,0.25) !important;
}

/* Enhanced Data Grid */
.enhanced-review-grid {
    border-radius: 12px;
    overflow: hidden;
}

.enhanced-review-grid .mud-table-head {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #495057;
}

.enhanced-review-grid .mud-table-row {
    transition: all 0.2s ease;
    border-bottom: 1px solid #e9ecef;
}

.enhanced-review-grid .mud-table-row:hover {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transform: translateX(4px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Status Cards */
.status-card {
    transition: all 0.3s ease;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.status-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.status-card:hover::before {
    transform: translateX(100%);
}

/* Action Cards */
.action-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Pending Review Action Card */
.pending-review {
    border-left: 4px solid #ff9800;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% { border-left-color: #ff9800; }
    50% { border-left-color: #f57c00; }
    100% { border-left-color: #ff9800; }
}

.pending-review:hover {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%) !important;
}

/* Reviewed Action Card */
.reviewed {
    border-left: 4px solid #4caf50;
}

.reviewed:hover {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c8 100%) !important;
}

/* Urgent Request Styling */
.urgent-request {
    animation: urgent-glow 2s infinite;
}

@keyframes urgent-glow {
    0% { box-shadow: 0 0 5px rgba(244, 67, 54, 0.3); }
    50% { box-shadow: 0 0 20px rgba(244, 67, 54, 0.6); }
    100% { box-shadow: 0 0 5px rgba(244, 67, 54, 0.3); }
}

/* Button Enhancements */
.action-card .mud-button {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.action-card .mud-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

/* Icon Button Enhancements */
.action-card .mud-icon-button {
    transition: all 0.2s ease;
    border-radius: 50%;
}

.action-card .mud-icon-button:hover {
    transform: scale(1.1);
}

/* Priority Badge Animation */
.mud-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Avatar Styling */
.mud-avatar {
    transition: all 0.3s ease;
}

.mud-avatar:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
}

/* Progress Circular Animation */
.mud-progress-circular {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

/* Chip Enhancements */
.mud-chip {
    transition: all 0.2s ease;
}

.mud-chip:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

/* Text Enhancements */
.mud-typography {
    transition: color 0.2s ease;
}

/* Loading State */
.mud-progress-circular {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* New Request Badge */
.new-request-badge {
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-3px); }
    60% { transform: translateY(-1px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-gradient {
        padding: 1rem !important;
    }
    
    .status-summary-card {
        min-width: 80px !important;
    }
    
    .action-card {
        padding: 0.5rem !important;
    }
    
    .enhanced-review-grid .mud-table-row:hover {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .enhanced-review-grid .mud-table-head {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
        color: #e2e8f0;
    }
    
    .enhanced-review-grid .mud-table-row:hover {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .status-card {
        border-color: #4a5568;
    }
}

/* Accessibility Enhancements */
.action-card:focus-within {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

.mud-button:focus {
    outline: 2px solid #1976d2;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .header-gradient,
    .action-card,
    .refresh-button {
        background: white !important;
        box-shadow: none !important;
    }
    
    .enhanced-review-grid .mud-table-row:hover {
        transform: none !important;
    }
    
    .urgent-request {
        animation: none !important;
    }
}
